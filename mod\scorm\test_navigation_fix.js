/**
 * Script de teste para verificar o funcionamento do fix de navegação SCORM
 * 
 * Para usar este script:
 * 1. <PERSON><PERSON> as ferramentas de desenvolvedor (F12) em uma página SCORM
 * 2. Cole este código no console
 * 3. Execute as funções de teste
 */

// Função para testar se o monitoramento está ativo
function testScormMonitoringActive() {
    console.log('=== Teste: Monitoramento Ativo ===');
    
    if (typeof window.scorm_completion_monitor_active !== 'undefined') {
        console.log('✓ Monitoramento está ativo:', window.scorm_completion_monitor_active);
    } else {
        console.log('✗ Monitoramento não foi inicializado');
    }
    
    if (typeof window.scorm_completion_interval !== 'undefined') {
        console.log('✓ Intervalo de verificação está configurado:', window.scorm_completion_interval);
    } else {
        console.log('✗ Intervalo de verificação não foi configurado');
    }
    
    if (typeof window.scorm_cm_id !== 'undefined') {
        console.log('✓ ID do módulo obtido:', window.scorm_cm_id);
    } else {
        console.log('✗ ID do módulo não foi obtido');
    }
}

// Função para testar a verificação de conclusão manualmente
function testCompletionCheck() {
    console.log('=== Teste: Verificação de Conclusão ===');
    
    if (!window.scorm_cm_id) {
        console.log('✗ ID do módulo não disponível');
        return;
    }
    
    // Testar endpoint nativo do Moodle
    var xhr = new XMLHttpRequest();
    var url = M.cfg.wwwroot + '/lib/ajax/service.php?sesskey=' + M.cfg.sesskey + 
             '&info=core_completion_get_activities_completion_status';
    
    xhr.open('POST', url, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    console.log('✓ Resposta da API de completion:', response);
                    
                    if (response && response.data && response.data.statuses) {
                        var cmStatus = response.data.statuses.find(function(status) {
                            return status.cmid == window.scorm_cm_id;
                        });
                        
                        if (cmStatus) {
                            console.log('✓ Status do módulo encontrado:', cmStatus);
                            console.log('  - Estado:', cmStatus.state);
                            console.log('  - Concluído:', cmStatus.state === 1 ? 'Sim' : 'Não');
                        } else {
                            console.log('✗ Status do módulo não encontrado');
                        }
                    }
                } catch (e) {
                    console.log('✗ Erro ao processar resposta:', e);
                }
            } else {
                console.log('✗ Erro na requisição:', xhr.status, xhr.statusText);
            }
        }
    };
    
    var requestData = [{
        "index": 0,
        "methodname": "core_completion_get_activities_completion_status",
        "args": {
            "courseid": M.cfg.courseid || 0,
            "userid": M.cfg.userid || 0
        }
    }];
    
    xhr.send(JSON.stringify(requestData));
}

// Função para testar o fallback do local_scormrefresh
function testScormRefreshFallback() {
    console.log('=== Teste: Fallback local_scormrefresh ===');
    
    if (!window.scorm_cm_id) {
        console.log('✗ ID do módulo não disponível');
        return;
    }
    
    var fallbackUrl = M.cfg.wwwroot + '/local/scormrefresh/ajax.php?cmid=' + 
                     window.scorm_cm_id + '&sesskey=' + M.cfg.sesskey;
    
    var xhr = new XMLHttpRequest();
    xhr.open('GET', fallbackUrl, true);
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    console.log('✓ Resposta do fallback scormrefresh:', response);
                    console.log('  - Sucesso:', response.success);
                    console.log('  - Estado de conclusão:', response.completionstate);
                } catch (e) {
                    console.log('✗ Erro ao processar resposta do fallback:', e);
                }
            } else {
                console.log('✗ Fallback não disponível (esperado se plugin não instalado)');
            }
        }
    };
    
    xhr.send();
}

// Função para testar os botões de navegação
function testNavigationButtons() {
    console.log('=== Teste: Botões de Navegação ===');
    
    var prevButton = document.getElementById('nav_prev');
    var nextButton = document.getElementById('nav_next');
    
    if (prevButton) {
        console.log('✓ Botão "Anterior" encontrado');
        console.log('  - Desabilitado:', prevButton.disabled);
        console.log('  - Classes:', prevButton.className);
    } else {
        console.log('✗ Botão "Anterior" não encontrado');
    }
    
    if (nextButton) {
        console.log('✓ Botão "Próximo" encontrado');
        console.log('  - Desabilitado:', nextButton.disabled);
        console.log('  - Classes:', nextButton.className);
    } else {
        console.log('✗ Botão "Próximo" não encontrado');
    }
}

// Função para simular conclusão e testar atualização
function simulateCompletionAndTest() {
    console.log('=== Teste: Simulação de Conclusão ===');
    
    // Forçar chamada da função scorm_fixnav se disponível
    if (typeof scorm_fixnav === 'function') {
        console.log('✓ Função scorm_fixnav disponível, executando...');
        scorm_fixnav();
        console.log('✓ scorm_fixnav executada');
        
        // Verificar estado dos botões após execução
        setTimeout(function() {
            testNavigationButtons();
        }, 100);
    } else {
        console.log('✗ Função scorm_fixnav não disponível');
    }
}

// Função para executar todos os testes
function runAllTests() {
    console.log('==========================================');
    console.log('INICIANDO TESTES DO FIX DE NAVEGAÇÃO SCORM');
    console.log('==========================================');
    
    testScormMonitoringActive();
    console.log('');
    
    testNavigationButtons();
    console.log('');
    
    testCompletionCheck();
    console.log('');
    
    testScormRefreshFallback();
    console.log('');
    
    simulateCompletionAndTest();
    
    console.log('==========================================');
    console.log('TESTES CONCLUÍDOS');
    console.log('==========================================');
}

// Executar testes automaticamente
console.log('Script de teste carregado. Execute runAllTests() para iniciar os testes.');
